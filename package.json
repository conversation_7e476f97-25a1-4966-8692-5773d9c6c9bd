{"name": "baofeng-rd", "version": "1.0.0", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon src/index.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "clean": "rm -rf dist", "calculate-rankings": "ts-node src/scripts/calculatePriceRankings.ts", "calculate-rankings:prod": "node dist/scripts/calculatePriceRankings.js"}, "keywords": ["api", "backend", "nodejs", "typescript", "express"], "author": "", "license": "ISC", "description": "Baofeng R&D Backend API Service", "engines": {"node": ">=18.0.0"}, "dependencies": {"axios": "^1.10.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "knex": "^3.1.0", "morgan": "^1.10.0", "mysql2": "^3.14.1", "nodemailer": "^7.0.3", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0", "winston": "^3.17.0", "xlsx": "^0.18.5", "zod": "^3.25.64"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.9", "@types/morgan": "^1.9.10", "@types/node": "^24.0.1", "@types/supertest": "^6.0.3", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@types/uuid": "^10.0.0", "jest": "^30.0.0", "nodemon": "^3.1.10", "supertest": "^7.1.1", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}