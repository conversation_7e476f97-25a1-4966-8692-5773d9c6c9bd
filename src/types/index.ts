import { Request } from 'express';

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  timestamp: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// User types
export interface User {
  id: string;
  phone?: string | null; // 手机号（微信授权获取）
  avatar?: string | undefined; // 头像URL
  role: UserRole;
  isActive: boolean;

  // 微信小程序相关字段
  wechatOpenId?: string | undefined; // 微信OpenID（主要标识）
  wechatUnionId?: string | undefined; // 微信UnionID
  wechatSessionKey?: string | undefined; // 微信会话密钥

  createdAt: Date;
  updatedAt: Date;
}

export enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
  MODERATOR = 'moderator',
  SUPER_ADMIN = 'super_admin',
}

export interface CreateUserDto {
  phone?: string; // 手机号（微信登录时可选，后续授权获取）
  avatar?: string;
  role?: UserRole;

  // 微信相关信息
  wechatOpenId?: string;
  wechatUnionId?: string;
  wechatSessionKey?: string;
}

export interface UpdateUserDto {
  phone?: string | null;
  avatar?: string | undefined;
  role?: UserRole;
  isActive?: boolean;

  // 微信相关信息
  wechatOpenId?: string | undefined;
  wechatUnionId?: string | undefined;
  wechatSessionKey?: string | undefined;
}

export interface LoginDto {
  phone: string;
  password: string;
}

// 微信小程序登录DTO
export interface WechatLoginDto {
  code: string; // 微信登录凭证
  encryptedData?: string; // 加密数据
  iv?: string; // 初始向量
  signature?: string; // 数据签名
  rawData?: string; // 原始数据
}

// 微信手机号授权DTO
export interface WechatPhoneDto {
  code: string; // 手机号授权凭证
}

export interface AuthTokens {
  accessToken: string;
  refreshToken?: string;
}

// JWT Payload
export interface JwtPayload {
  userId: string;
  phone?: string;
  role: UserRole;
  iat?: number;
  exp?: number;
}

// Extended Request with user
export interface AuthenticatedRequest extends Request {
  user?: JwtPayload;
}

// Contact types
export enum ContactCategory {
  FRIEND = 'FRIEND',
  FAMILY = 'FAMILY',
  COLLEAGUE = 'COLLEAGUE',
  BUSINESS = 'BUSINESS',
  OTHER = 'OTHER'
}

export enum ContactFrequency {
  DAILY = 'DAILY',
  WEEKLY = 'WEEKLY',
  MONTHLY = 'MONTHLY',
  RARELY = 'RARELY',
  NEVER = 'NEVER'
}

export enum ContactType {
  CALL = 'CALL',
  MESSAGE = 'MESSAGE',
  WECHAT = 'WECHAT',
  EMAIL = 'EMAIL',
  MEETING = 'MEETING',
  OTHER = 'OTHER'
}

export enum ShareType {
  QR_CODE = 'QR_CODE',
  LINK = 'LINK',
  CARD = 'CARD',
  MINI_PROGRAM = 'MINI_PROGRAM'
}

export enum ShareScope {
  PUBLIC = 'PUBLIC',
  FRIENDS = 'FRIENDS',
  PRIVATE = 'PRIVATE'
}

export enum AccessType {
  VIEW = 'VIEW',
  SHARE = 'SHARE',
  DOWNLOAD = 'DOWNLOAD',
  SAVE = 'SAVE'
}

export interface Contact {
  id: number;
  userId: string;
  name: string;
  phone: string;
  wechatId?: string;
  avatarUrl?: string;
  category: ContactCategory;
  tags?: string[];
  company?: string;
  position?: string;
  email?: string;
  address?: string;
  birthday?: Date;
  notes?: string;
  contactFrequency: ContactFrequency;
  importanceLevel: number; // 1-5
  isFavorite: boolean;
  lastContactAt?: Date;
  lastContactType?: ContactType;
  contactCount: number;
  isPublic: boolean;
  sharePhone: boolean;
  shareWechat: boolean;
  status: number; // 1-正常，0-已删除
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateContactDto {
  name: string;
  phone: string;
  wechatId?: string;
  avatarUrl?: string;
  category?: ContactCategory;
  tags?: string[];
  company?: string;
  position?: string;
  email?: string;
  address?: string;
  birthday?: Date;
  notes?: string;
  contactFrequency?: ContactFrequency;
  importanceLevel?: number;
  isFavorite?: boolean;
  isPublic?: boolean;
  sharePhone?: boolean;
  shareWechat?: boolean;
}

export interface UpdateContactDto {
  name?: string;
  phone?: string;
  wechatId?: string;
  avatarUrl?: string;
  category?: ContactCategory;
  tags?: string[];
  company?: string;
  position?: string;
  email?: string;
  address?: string;
  birthday?: Date;
  notes?: string;
  contactFrequency?: ContactFrequency;
  importanceLevel?: number;
  isFavorite?: boolean;
  lastContactAt?: Date;
  lastContactType?: ContactType;
  isPublic?: boolean;
  sharePhone?: boolean;
  shareWechat?: boolean;
}

export interface ContactQuery {
  page?: number;
  limit?: number;
  search?: string; // 搜索姓名、手机号、微信号
  category?: ContactCategory;
  isFavorite?: boolean;
  importanceLevel?: number;
  isPublic?: boolean;
  tags?: string[];
  sortBy?: 'name' | 'createdAt' | 'lastContactAt' | 'importanceLevel' | 'contactCount';
  sortOrder?: 'asc' | 'desc';
}

export interface ShareConfig {
  id: number;
  userId: string;
  contactId: number;
  shareType: ShareType;
  shareTitle?: string;
  shareDescription?: string;
  shareImageUrl?: string;
  shareScope: ShareScope;
  allowedUsers?: string[];
  includePhone: boolean;
  includeWechat: boolean;
  includeEmail: boolean;
  includeCompany: boolean;
  includeAddress: boolean;
  includeNotes: boolean;
  shareCount: number;
  viewCount: number;
  lastSharedAt?: Date;
  lastViewedAt?: Date;
  expiresAt?: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateShareConfigDto {
  contactId: number;
  shareType: ShareType;
  shareTitle?: string;
  shareDescription?: string;
  shareImageUrl?: string;
  shareScope?: ShareScope;
  allowedUsers?: string[];
  includePhone?: boolean;
  includeWechat?: boolean;
  includeEmail?: boolean;
  includeCompany?: boolean;
  includeAddress?: boolean;
  includeNotes?: boolean;
  expiresAt?: Date;
}

export interface UpdateShareConfigDto {
  shareTitle?: string;
  shareDescription?: string;
  shareImageUrl?: string;
  shareScope?: ShareScope;
  allowedUsers?: string[];
  includePhone?: boolean;
  includeWechat?: boolean;
  includeEmail?: boolean;
  includeCompany?: boolean;
  includeAddress?: boolean;
  includeNotes?: boolean;
  expiresAt?: Date;
  isActive?: boolean;
}

export interface AccessLog {
  id: number;
  contactId: number;
  shareConfigId?: number;
  visitorUserId?: string;
  visitorIp?: string;
  accessType: AccessType;
  userAgent?: string;
  referer?: string;
  createdAt: Date;
}

export interface ContactStats {
  totalContacts: number;
  favoriteContacts: number;
  publicContacts: number;
  categoryCounts: Record<ContactCategory, number>;
  importanceLevelCounts: Record<number, number>;
  recentContacts: number; // 最近30天联系的人数
  topTags: Array<{ tag: string; count: number }>;
}

// Query parameters
export interface PaginationQuery {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface UserQuery extends PaginationQuery {
  search?: string;
  role?: UserRole;
  isActive?: boolean;
}

// Admin types
export interface Admin {
  id: string;
  email: string;
  username: string;
  firstName?: string | undefined;
  lastName?: string | undefined;
  avatar?: string | undefined;
  role: AdminRole;
  isActive: boolean;
  permissions?: Record<string, string[]> | undefined;
  lastLoginAt?: Date | undefined;
  lastLoginIp?: string | undefined;
  createdBy?: string | undefined;
  createdAt: Date;
  updatedAt: Date;
}

export enum AdminRole {
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin',
  MODERATOR = 'moderator',
}

export interface CreateAdminDto {
  email: string;
  username: string;
  password: string;
  firstName?: string;
  lastName?: string;
  role: AdminRole;
  permissions?: Record<string, string[]>;
  createdBy?: string;
}

export interface UpdateAdminDto {
  email?: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  avatar?: string;
  role?: AdminRole;
  isActive?: boolean;
  permissions?: Record<string, string[]>;
}

export interface AdminLoginDto {
  email?: string;
  username?: string;
  password: string;
}

export interface AdminQuery extends PaginationQuery {
  search?: string;
  role?: AdminRole;
  isActive?: boolean;
}

// Error types
export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;

  constructor(message: string, statusCode: number = 500, isOperational: boolean = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;

    Error.captureStackTrace(this, this.constructor);
  }
}

// Category types
export interface Category {
  id: number;
  name: string;
  fullPath: string;
  level: number;
  parentId?: number | null;
  sortIndex: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateCategoryDto {
  name: string;
  parentId?: number | null;
  sortIndex?: number;
}

export interface UpdateCategoryDto {
  name?: string;
  parentId?: number | null;
  sortIndex?: number;
}

export interface CategoryQuery extends PaginationQuery {
  search?: string;
  level?: number;
  parentId?: number;
}

export interface CategoryTree extends Category {
  children?: CategoryTree[];
}

export enum CategoryLevel {
  BRAND = 1,
  MODEL = 2,
  SUB_MODEL = 3,
}

// Database types (for future use with Prisma or other ORMs)
export interface DatabaseConfig {
  url?: string;
  host?: string;
  port?: number;
  database?: string;
  username?: string;
  password?: string;
}
