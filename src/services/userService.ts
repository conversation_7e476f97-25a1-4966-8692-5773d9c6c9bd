import { User, CreateUserDto, UpdateUserDto, UserQuery, AppError } from '../types';
import { UserModel } from '../models/User';

export class UserService {
  /**
   * Create a new user
   */
  static async createUser(userData: CreateUserDto): Promise<User> {
    try {
      return await UserModel.create(userData);
    } catch (error) {
      if (error instanceof Error && error.message.includes('already exists')) {
        throw new AppError(error.message, 409);
      }
      throw new AppError('Failed to create user', 500);
    }
  }

  /**
   * Get user by ID
   */
  static async getUserById(id: string): Promise<User> {
    const user = await UserModel.findById(id);
    
    if (!user) {
      throw new AppError('User not found', 404);
    }

    return user;
  }

  /**
   * Get user by email
   */
  static async getUserByEmail(email: string): Promise<User> {
    const user = await UserModel.findByEmail(email);
    
    if (!user) {
      throw new AppError('User not found', 404);
    }

    return user;
  }

  /**
   * Update user
   */
  static async updateUser(id: string, updateData: UpdateUserDto): Promise<User> {
    try {
      const user = await UserModel.update(id, updateData);
      
      if (!user) {
        throw new AppError('User not found', 404);
      }

      return user;
    } catch (error) {
      if (error instanceof AppError) {
        throw error;
      }
      if (error instanceof Error && error.message.includes('already exists')) {
        throw new AppError(error.message, 409);
      }
      throw new AppError('Failed to update user', 500);
    }
  }

  /**
   * Delete user
   */
  static async deleteUser(id: string): Promise<void> {
    const deleted = await UserModel.delete(id);
    
    if (!deleted) {
      throw new AppError('User not found', 404);
    }
  }

  /**
   * Get users with pagination and filtering
   */
  static async getUsers(query: UserQuery): Promise<{ users: User[]; total: number }> {
    return await UserModel.findMany(query);
  }

  /**
   * Check if user exists by email
   */
  static async userExistsByEmail(email: string): Promise<boolean> {
    const user = await UserModel.findByEmail(email);
    return !!user;
  }

  /**
   * Check if user exists by username
   */
  static async userExistsByUsername(username: string): Promise<boolean> {
    const user = await UserModel.findByUsername(username);
    return !!user;
  }
}
