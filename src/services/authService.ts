import { LoginDto, AuthTokens, User, CreateUserDto, UpdateUserDto, AppError, JwtPayload } from '../types';
import { UserModel } from '../models/User';
import { UserService } from './userService';
import { generateToken, verifyToken } from '../utils/jwt';
import logger from '../config/logger';

export class AuthService {
  /**
   * Register new user
   */
  static async register(userData: CreateUserDto): Promise<{ user: User; tokens: AuthTokens }> {
    const user = await UserService.createUser(userData);
    const tokens = this.generateTokens(user);
    
    return { user, tokens };
  }

  /**
   * Login user
   */
  static async login(loginData: LoginDto): Promise<{ user: User; tokens: AuthTokens }> {
    let user: User | null = null;

    // Find user by phone or email
    if (loginData.phone) {
      user = await UserModel.findByPhone(loginData.phone);
    } else if (loginData.email) {
      user = await UserModel.findByEmail(loginData.email);
    }

    if (!user) {
      throw new AppError('Invalid credentials', 401);
    }

    // Note: In a real implementation, you would store the hashed password
    // and compare it here. For this demo, we'll simulate password verification
    // by checking if the password matches a simple pattern
    const identifier = loginData.phone || loginData.email || user.email || '';
    const isPasswordValid = await this.verifyPassword(loginData.password, identifier);

    if (!isPasswordValid) {
      throw new AppError('Invalid credentials', 401);
    }

    // Update last login
    await UserModel.updateLastLogin(user.id);

    const tokens = this.generateTokens(user);
    return { user, tokens };
  }

  /**
   * Refresh access token
   */
  static async refreshToken(refreshToken: string): Promise<AuthTokens> {
    try {
      const payload = verifyToken(refreshToken) as JwtPayload;
      const user = await UserModel.findById(payload.userId);
      
      if (!user) {
        throw new AppError('User not found', 404);
      }

      return this.generateTokens(user);
    } catch (error) {
      throw new AppError('Invalid refresh token', 401);
    }
  }

  /**
   * Logout user
   */
  static async logout(userId: string): Promise<void> {
    try {
      // 在实际实现中，这里应该：
      // 1. 将用户的 refresh token 加入黑名单
      // 2. 或者从数据库中删除用户的会话记录
      // 3. 或者更新用户的 token 版本号

      // 目前的简单实现：记录登出操作
      logger.info(`User ${userId} logged out successfully`);

      // TODO: 实现 token 黑名单或会话管理
      // 可以在这里添加以下逻辑：
      // - 将 refresh token 添加到 Redis 黑名单
      // - 从数据库中删除用户会话
      // - 更新用户最后活动时间

      return;
    } catch (error) {
      logger.error('Logout error:', error);
      throw new AppError('Logout failed', 500);
    }
  }

  /**
   * Get user profile
   */
  static async getProfile(userId: string): Promise<User> {
    const user = await UserModel.findById(userId);
    if (!user) {
      throw new AppError('User not found', 404);
    }
    return user;
  }

  /**
   * Update user profile
   */
  static async updateProfile(userId: string, updateData: UpdateUserDto): Promise<User> {
    return await UserService.updateUser(userId, updateData);
  }

  /**
   * Generate JWT tokens for user
   */
  private static generateTokens(user: User): AuthTokens {
    const payload: Omit<JwtPayload, 'iat' | 'exp'> = {
      userId: user.id,
      phone: user.phone || undefined,
      email: user.email || '',
      role: user.role,
    };

    const accessToken = generateToken(payload);

    return {
      accessToken,
      refreshToken: accessToken, // For simplicity, using same token
    };
  }

  /**
   * Verify password (simplified for demo)
   */
  private static async verifyPassword(password: string, identifier: string): Promise<boolean> {
    // For demo purposes, accept any password that's at least 6 characters
    return Boolean(password && password.length >= 6);
  }
}
