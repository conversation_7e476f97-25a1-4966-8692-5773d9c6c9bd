import { v4 as uuidv4 } from 'uuid';
import { WechatLoginDto, WechatPhoneDto, User, AuthTokens, AppError, CreateUserDto, UserRole } from '../types';
import { WechatUtils } from '../utils/wechat';
import { WechatUserInfo } from '../config/wechat';
import { UserModel } from '../models/User';
import { generateToken } from '../utils/jwt';
import logger from '../config/logger';

/**
 * 微信小程序服务类
 */
export class WechatService {
  /**
   * 微信小程序登录
   * @param loginData 微信登录数据
   */
  static async login(loginData: WechatLoginDto): Promise<{ user: User; tokens: AuthTokens; isNewUser: boolean }> {
    try {
      // 1. 调用微信API获取用户信息
      const wechatLoginResult = await WechatUtils.login(loginData.code);
      
      if (!wechatLoginResult.openid) {
        throw new AppError('微信登录失败：无法获取用户OpenID', 400);
      }

      // 2. 构建微信用户信息（登录时只有基本信息）
      const wechatUserInfo: WechatUserInfo = {
        openId: wechatLoginResult.openid,
        unionId: wechatLoginResult.unionid,
        sessionKey: wechatLoginResult.session_key!,
      };

      // 3. 如果有加密数据，解密获取用户详细信息
      if (loginData.encryptedData && loginData.iv && wechatLoginResult.session_key) {
        try {
          const decryptedData = WechatUtils.decryptData(
            loginData.encryptedData,
            loginData.iv,
            wechatLoginResult.session_key
          );
          
          // 验证签名（如果提供）
          if (loginData.signature && loginData.rawData) {
            const isValidSignature = WechatUtils.verifySignature(
              loginData.rawData,
              wechatLoginResult.session_key,
              loginData.signature
            );
            
            if (!isValidSignature) {
              logger.warn('WeChat signature verification failed');
            }
          }

          // 更新用户信息
          wechatUserInfo.nickname = decryptedData.nickName;
          wechatUserInfo.avatarUrl = decryptedData.avatarUrl;
          wechatUserInfo.gender = decryptedData.gender;
          wechatUserInfo.city = decryptedData.city;
          wechatUserInfo.province = decryptedData.province;
          wechatUserInfo.country = decryptedData.country;
          wechatUserInfo.language = decryptedData.language;
        } catch (error) {
          logger.warn('Failed to decrypt WeChat user data:', error);
          // 继续执行，不影响登录流程
        }
      }

      // 4. 查找或创建用户
      let user = await UserModel.findByWechatOpenId(wechatUserInfo.openId);
      let isNewUser = false;

      if (!user) {
        // 创建新用户（微信登录时只有基本信息）
        const createUserData: CreateUserDto = {
          phone: undefined, // 微信登录时手机号为空，需要后续授权获取
          wechatOpenId: wechatUserInfo.openId,
          wechatUnionId: wechatUserInfo.unionId,
          wechatSessionKey: wechatUserInfo.sessionKey,
          role: UserRole.USER,
          // 其他用户信息在登录时不可用，需要用户授权后获取
        };

        user = await UserModel.create(createUserData);
        isNewUser = true;
        logger.info(`New WeChat user created: ${user.id}`);
      } else {
        // 更新现有用户的微信会话信息
        const updateData: Partial<User> = {
          wechatSessionKey: wechatUserInfo.sessionKey,
          lastLoginAt: new Date(),
        };

        const updatedUser = await UserModel.update(user.id, updateData);
        if (!updatedUser) {
          throw new AppError('用户更新失败', 500);
        }
        user = updatedUser;
        logger.info(`WeChat user updated: ${user.id}`);
      }

      // 5. 生成JWT令牌
      const tokens = this.generateTokens(user);

      return { user, tokens, isNewUser };
    } catch (error) {
      logger.error('WeChat login service error:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('微信登录失败', 500);
    }
  }

  /**
   * 获取微信小程序用户手机号
   * @param userId 用户ID
   * @param phoneData 手机号授权数据
   */
  static async getPhoneNumber(userId: string, phoneData: WechatPhoneDto): Promise<User> {
    try {
      // 1. 获取用户信息
      const user = await UserModel.findById(userId);
      if (!user) {
        throw new AppError('用户不存在', 404);
      }

      if (!user.wechatOpenId) {
        throw new AppError('用户未绑定微信', 400);
      }

      // 2. 调用微信API获取手机号
      const phoneResult = await WechatUtils.getPhoneNumber(phoneData.code);
      
      if (!phoneResult.phone_info?.phoneNumber) {
        throw new AppError('获取手机号失败', 400);
      }

      const phoneNumber = phoneResult.phone_info.phoneNumber;

      // 3. 检查手机号是否已被其他用户使用
      const existingUser = await UserModel.findByPhone(phoneNumber);
      if (existingUser && existingUser.id !== userId) {
        throw new AppError('该手机号已被其他用户使用', 400);
      }

      // 4. 更新用户手机号
      const updatedUser = await UserModel.update(userId, {
        phone: phoneNumber,
      });

      if (!updatedUser) {
        throw new AppError('用户更新失败', 500);
      }

      logger.info(`User phone number updated: ${userId} -> ${phoneNumber}`);
      return updatedUser;
    } catch (error) {
      logger.error('Get WeChat phone number service error:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('获取手机号失败', 500);
    }
  }

  /**
   * 通过加密数据获取手机号（旧版本API）
   * @param userId 用户ID
   * @param phoneData 手机号加密数据
   */
  static async getPhoneNumberByEncryptedData(
    userId: string, 
    phoneData: { encryptedData: string; iv: string }
  ): Promise<User> {
    try {
      // 1. 获取用户信息
      const user = await UserModel.findById(userId);
      if (!user) {
        throw new AppError('用户不存在', 404);
      }

      if (!user.wechatSessionKey) {
        throw new AppError('用户会话已过期，请重新登录', 401);
      }

      // 2. 解密手机号数据
      const decryptedData = WechatUtils.decryptData(
        phoneData.encryptedData,
        phoneData.iv,
        user.wechatSessionKey
      );

      if (!decryptedData.phoneNumber) {
        throw new AppError('解密手机号失败', 400);
      }

      const phoneNumber = decryptedData.phoneNumber;

      // 3. 检查手机号是否已被其他用户使用
      const existingUser = await UserModel.findByPhone(phoneNumber);
      if (existingUser && existingUser.id !== userId) {
        throw new AppError('该手机号已被其他用户使用', 400);
      }

      // 4. 更新用户手机号
      const updatedUser = await UserModel.update(userId, {
        phone: phoneNumber,
      });

      if (!updatedUser) {
        throw new AppError('用户更新失败', 500);
      }

      logger.info(`User phone number updated via encrypted data: ${userId} -> ${phoneNumber}`);
      return updatedUser;
    } catch (error) {
      logger.error('Get WeChat phone number by encrypted data service error:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('获取手机号失败', 500);
    }
  }

  /**
   * 生成JWT令牌
   * @param user 用户信息
   */
  private static generateTokens(user: User): AuthTokens {
    const payload = {
      userId: user.id,
      phone: user.phone || undefined,
      email: user.email,
      role: user.role,
    };

    const accessToken = generateToken(payload);
    
    return {
      accessToken,
      // 可以在这里添加refresh token逻辑
    };
  }

  /**
   * 测试更新用户手机号（仅开发环境）
   * @param userId 用户ID
   * @param phone 手机号
   */
  static async testUpdatePhone(userId: string, phone: string): Promise<User> {
    try {
      // 1. 获取用户信息
      const user = await UserModel.findById(userId);
      if (!user) {
        throw new AppError('用户不存在', 404);
      }

      if (!user.wechatOpenId) {
        throw new AppError('用户未绑定微信', 400);
      }

      // 2. 检查手机号是否已被其他用户使用
      const existingUser = await UserModel.findByPhone(phone);
      if (existingUser && existingUser.id !== userId) {
        throw new AppError('该手机号已被其他用户使用', 400);
      }

      // 3. 更新用户手机号
      const updatedUser = await UserModel.update(userId, {
        phone: phone,
      });

      if (!updatedUser) {
        throw new AppError('用户更新失败', 500);
      }

      logger.info(`Test phone number updated: ${userId} -> ${phone}`);
      return updatedUser;
    } catch (error) {
      logger.error('Test update phone number service error:', error);
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError('测试更新手机号失败', 500);
    }
  }

  /**
   * 映射微信性别到系统性别
   * @param wechatGender 微信性别 (0: 未知, 1: 男, 2: 女)
   */
  private static mapWechatGender(wechatGender?: number): number | null {
    switch (wechatGender) {
      case 1:
        return 1; // 男
      case 2:
        return 2; // 女
      case 0:
        return 0; // 未知
      default:
        return null; // 未提供
    }
  }
}
