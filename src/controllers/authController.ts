import { Request, Response } from 'express';
import { AuthenticatedRequest } from '../types';
import { AuthService } from '../services/authService';
import { sendSuccess, sendCreated, sendError } from '../utils/response';
import { asyncHandler } from '../middleware/errorHandler';
import { AdminModel } from '../models/Admin';
import jwt from 'jsonwebtoken';
import config from '../config';

export class AuthController {
  /**
   * Register new user
   */
  static register = asyncHandler(
    async (req: Request, res: Response) => {
      const userData = req.body;
      const { user, tokens } = await AuthService.register(userData);

      // Remove sensitive data from response
      const { ...userResponse } = user;

      sendCreated(res, {
        user: userResponse,
        tokens,
      }, 'User registered successfully');
    }
  );

  /**
   * Login user
   */
  static login = asyncHandler(
    async (req: Request, res: Response) => {
      const loginData = req.body;
      const { user, tokens } = await AuthService.login(loginData);
      console.log(loginData)
      // Remove sensitive data from response
      const { ...userResponse } = user;

      sendSuccess(res, {
        user: userResponse,
        tokens,
      }, 'Login successful');
    }
  );

  /**
   * Refresh access token
   */
  static refreshToken = asyncHandler(
    async (req: Request, res: Response) => {
      const { refreshToken } = req.body;
      const tokens = await AuthService.refreshToken(refreshToken);

      sendSuccess(res, { tokens }, 'Token refreshed successfully');
    }
  );

  /**
   * Logout user
   */
  static logout = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.user!.userId;
      await AuthService.logout(userId);

      sendSuccess(res, null, 'Logout successful');
    }
  );

  /**
   * Get current user profile
   */
  static getProfile = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.user!.userId;
      const user = await AuthService.getProfile(userId);

      sendSuccess(res, user, 'Profile retrieved successfully');
    }
  );

  /**
   * Update current user profile
   */
  static updateProfile = asyncHandler(
    async (req: AuthenticatedRequest, res: Response) => {
      const userId = req.user!.userId;
      const updateData = req.body;

      const user = await AuthService.updateProfile(userId, updateData);

      sendSuccess(res, user, 'Profile updated successfully');
    }
  );

  /**
   * 获取当前登录用户信息 (通用接口)
   * 根据token类型自动识别是管理员还是普通用户
   */
  static getCurrentUser = asyncHandler(
    async (req: Request, res: Response) => {
      try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          return sendError(res, 'No token provided', 401);
        }

        const token = authHeader.substring(7);

        try {
          // 尝试验证token
          const decoded = jwt.verify(token, config.jwt.secret) as any;

          // 根据token中的信息判断用户类型
          // 检查是否为管理员token：包含adminId、type为admin、或者role包含admin
          const isAdminToken = decoded.type === 'admin' ||
                              decoded.adminId ||
                              (decoded.role && (decoded.role.includes('admin') || decoded.role === 'super_admin')) ||
                              (decoded.userId && decoded.userId.startsWith('admin-'));

          if (isAdminToken) {
            // 管理员token
            const adminId = decoded.adminId || decoded.userId || decoded.id;
            const admin = await AdminModel.findById(adminId);

            if (!admin) {
              return sendError(res, 'Admin not found', 404);
            }

            // 移除敏感信息 (password_hash 在数据库中存在但不在类型定义中)
            const adminData = { ...admin };

            return sendSuccess(res, {
              userType: 'admin',
              ...adminData
            }, 'Admin user info retrieved successfully');
          } else {
            // 普通用户token (暂时返回错误，因为还没有实现用户系统)
            return sendError(res, 'User system not implemented yet', 501);
          }
        } catch (jwtError) {
          return sendError(res, 'Invalid token', 401);
        }
      } catch (error) {
        console.error('Get current user error:', error);
        return sendError(res, 'Internal server error', 500);
      }
    }
  );
}
