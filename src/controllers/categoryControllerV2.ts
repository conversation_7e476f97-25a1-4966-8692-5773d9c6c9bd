import { Request, Response } from 'express';
import db from '../config/knex';
import { sendSuccess, sendError } from '../utils/response';
import { asyncHandler } from '../middleware/errorHandler';
import logger from '../config/logger';

export class CategoryControllerV2 {
  /**
   * Get categories (root categories)
   * GET /api/v1/categories
   */
  static getCategories = asyncHandler(
    async (_req: Request, res: Response) => {
      // 查询根类目 (level = 0)
      const categories = await db('phone_categories')
        .where('level', 0)
        .where('status', 1)
        .orderBy(['sort_index', 'id']);

      sendSuccess(res, {
        list: categories,
        pagination: {
          page: 1,
          limit: categories.length,
          total: categories.length,
          totalPages: 1,
        },
      }, 'Categories retrieved successfully');
    }
  );

  /**
   * Get brands by category ID with tree structure (brands -> models -> sub-models)
   * GET /api/v1/categories/:categoryId/brands
   */
  static getBrandsByCategory = asyncHandler(
    async (req: Request, res: Response) => {
      const categoryId = parseInt(req.params.categoryId as string);

      // 查询指定分类下的品牌 (level = 1, parent_id = categoryId)
      const brands = await db('phone_categories')
        .where('level', 1)
        .where('parent_id', categoryId)
        .where('status', 1)
        .orderBy(['sort_index', 'id']);

      // 为每个品牌构建树形结构，包含型号和子型号
      const brandTree = [];
      for (const brand of brands) {
        const brandNode = { ...brand, models: [] };

        // 获取该品牌下的型号 (level = 2, parent_id = brand.id)
        const models = await db('phone_categories')
          .where('level', 2)
          .where('parent_id', brand.id)
          .where('status', 1)
          .orderBy(['sort_index', 'id']);

        for (const model of models) {
          const modelNode = { ...model, sub_models: [] };

          // 获取该型号下的子型号 (level = 3, parent_id = model.id)
          const subModels = await db('phone_categories')
            .where('level', 3)
            .where('parent_id', model.id)
            .where('status', 1)
            .orderBy(['sort_index', 'id']);

          modelNode.sub_models = subModels;
          brandNode.models.push(modelNode);
        }

        brandTree.push(brandNode);
      }

      sendSuccess(res, {
        list: brandTree,
        total: brandTree.length,
      }, '');
    }
  );

  /**
   * Get all brands (phone_categories level=1) - 保持向后兼容
   * GET /api/v1/categories/brands
   */
  static getBrands = asyncHandler(
    async (req: Request, res: Response) => {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 50;
      const search = req.query.search as string;
      const parentId = req.query.parent_id as string;
      const offset = (page - 1) * limit;

      let query = db('phone_categories')
        .where('level', 1)
        .where('status', 1)
        .orderBy(['sort_index', 'id']);

      // Add search by name
      if (search) {
        query = query.where('name', 'like', `%${search}%`);
      }

      // Add search by parent_id
      if (parentId) {
        query = query.where('parent_id', parentId);
      }

      const rows = await query.limit(limit).offset(offset);

      // Get total count with same filters
      let countQuery = db('phone_categories')
        .where('level', 1)
        .where('status', 1)
        .count('* as total');
      if (search) {
        countQuery = countQuery.where('name', 'like', `%${search}%`);
      }
      if (parentId) {
        countQuery = countQuery.where('parent_id', parentId);
      }
      const countResult = await countQuery;
      const total = countResult[0] ? Number(countResult[0]['total']) : 0;

      sendSuccess(res, {
          list:rows,
          pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit),
          },
      }, 'Brands retrieved successfully');
    }
  );

  /**
   * Get models by brand ID
   * GET /api/v1/categories/brands/:brandId/models
   */
  static getModelsByBrand = asyncHandler(
    async (req: Request, res: Response) => {
      const brandId = parseInt(req.params.brandId as string);
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 50;
      const search = req.query.search as string;
      const offset = (page - 1) * limit;

      let query = db('phone_categories')
        .where('level', 2)
        .where('parent_id', brandId)
        .where('status', 1)
        .orderBy(['sort_index', 'id']);

      if (search) {
        query = query.where('name', 'like', `%${search}%`);
      }

      const rows = await query.limit(limit).offset(offset);

      // Get total count
      let countQuery = db('phone_categories')
        .where('level', 2)
        .where('parent_id', brandId)
        .where('status', 1)
        .count('* as total');
      if (search) {
        countQuery = countQuery.where('name', 'like', `%${search}%`);
      }
      const countResult = await countQuery;
      const total = countResult[0] ? Number(countResult[0]['total']) : 0;

      sendSuccess(res, {
        data: rows,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      }, 'Models retrieved successfully');
    }
  );

  /**
   * Get sub-models by model ID
   * GET /api/v1/categories/models/:modelId/sub-models
   */
  static getSubModelsByModel = asyncHandler(
    async (req: Request, res: Response) => {
      const modelId = parseInt(req.params.modelId as string);
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 50;
      const search = req.query.search as string;
      const offset = (page - 1) * limit;

      let query = db('phone_categories')
        .where('level', 3)
        .where('parent_id', modelId)
        .where('status', 1)
        .orderBy(['sort_index', 'id']);

      if (search) {
        query = query.where('name', 'like', `%${search}%`);
      }

      const rows = await query.limit(limit).offset(offset);

      // Get total count
      let countQuery = db('phone_categories')
        .where('level', 3)
        .where('parent_id', modelId)
        .where('status', 1)
        .count('* as total');
      if (search) {
        countQuery = countQuery.where('name', 'like', `%${search}%`);
      }
      const countResult = await countQuery;
      const total = countResult[0] ? Number(countResult[0]['total']) : 0;

      sendSuccess(res, {
        data: rows,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      }, 'Sub-models retrieved successfully');
    }
  );

  /**
   * Get category tree
   * GET /api/v1/categories/tree
   */
  static getCategoryTree = asyncHandler(
    async (_req: Request, res: Response) => {
      const id = _req.params.id || 1
      // 获取所有品牌 (level = 1)
      const brands = await db('phone_categories')
        .where('level', 1)
        .where('status', 1)
        .where('parent_id',id)
        .orderBy(['sort_index', 'id']);

      const tree = [];
      for (const brand of brands) {
        const brandNode = { ...brand, children: [] };

        // 获取该品牌下的型号 (level = 2)
        const models = await db('phone_categories')
          .where('level', 2)
          .where('parent_id', brand.id)
          .where('status', 1)
          .orderBy(['sort_index', 'id']);

        for (const model of models) {
          const modelNode = { ...model, children: [] };

          // 获取该型号下的子型号 (level = 3)
          const subModels = await db('phone_categories')
            .where('level', 3)
            .where('parent_id', model.id)
            .where('status', 1)
            .orderBy(['sort_index', 'id']);

          modelNode.children = subModels;
          brandNode.children.push(modelNode);
        }

        tree.push(brandNode);
      }

      sendSuccess(res, tree, 'Category tree retrieved successfully');
    }
  );

  /**
   * Soft delete brand (假删除)
   * PUT /api/v1/categories/brands/:id/delete
   */
  static softDeleteBrand = asyncHandler(
    async (req: Request, res: Response) => {
      const id = parseInt(req.params.id as string);

      // Check if brand exists (level = 1)
      const existing = await db('phone_categories')
        .where('id', id)
        .where('level', 1)
        .first();
      if (!existing) {
        sendError(res, 'Brand not found', 404);
        return;
      }

      // Update the brand to mark as deleted (假删除)
      const deletedName = existing.name.startsWith('[已删除]') ? existing.name : `[已删除]${existing.name}`;

      await db('phone_categories')
        .where('id', id)
        .update({
          name: deletedName,
          status: 0, // 设置为禁用状态
          updated_at: db.fn.now()
        });

      sendSuccess(res, null, 'Brand soft deleted successfully');
    }
  );

  /**
   * Soft delete model (假删除)
   * PUT /api/v1/categories/models/:id/delete
   */
  static softDeleteModel = asyncHandler(
    async (req: Request, res: Response) => {
      const id = parseInt(req.params.id as string);

      // Check if model exists (level = 2)
      const existing = await db('phone_categories')
        .where('id', id)
        .where('level', 2)
        .first();
      if (!existing) {
        sendError(res, 'Model not found', 404);
        return;
      }

      // Update the model to mark as deleted (假删除)
      const deletedName = existing.name.startsWith('[已删除]') ? existing.name : `[已删除]${existing.name}`;

      await db('phone_categories')
        .where('id', id)
        .update({
          name: deletedName,
          status: 0, // 设置为禁用状态
          updated_at: db.fn.now()
        });

      sendSuccess(res, null, 'Model soft deleted successfully');
    }
  );

  /**
   * Soft delete sub-model (假删除)
   * PUT /api/v1/categories/sub-models/:id/delete
   */
  static softDeleteSubModel = asyncHandler(
    async (req: Request, res: Response) => {
      const id = parseInt(req.params.id as string);

      // Check if sub-model exists (level = 3)
      const existing = await db('phone_categories')
        .where('id', id)
        .where('level', 3)
        .first();
      if (!existing) {
        sendError(res, 'Sub-model not found', 404);
        return;
      }

      // Update the sub-model to mark as deleted (假删除)
      const deletedName = existing.name.startsWith('[已删除]') ? existing.name : `[已删除]${existing.name}`;

      await db('phone_categories')
        .where('id', id)
        .update({
          name: deletedName,
          status: 0, // 设置为禁用状态
          updated_at: db.fn.now()
        });

      sendSuccess(res, null, 'Sub-model soft deleted successfully');
    }
  );

  /**
   * Get category details by ID and table
   * GET /api/v1/categories/:table/:id
   */
  static getCategoryDetails = asyncHandler(
    async (req: Request, res: Response) => {
      const table = req.params.table as string;
      const id = parseInt(req.params.id as string);

      // Map old table names to levels for backward compatibility
      const tableToLevel: { [key: string]: number } = {
        'phone_brands': 1,
        'phone_models': 2,
        'phone_sub_models': 3
      };

      if (!tableToLevel[table]) {
        sendError(res, 'Invalid table name', 400);
        return;
      }

      const level = tableToLevel[table];

      // Get category details from phone_categories table
      const category = await db('phone_categories')
        .where('id', id)
        .where('level', level)
        .first();
      if (!category) {
        sendError(res, 'Category not found', 404);
        return;
      }

      // Get children count
      let childrenCount = 0;
      if (level < 3) { // Only levels 0, 1, 2 can have children
        const result = await db('phone_categories')
          .where('parent_id', id)
          .where('level', level + 1)
          .where('status', 1)
          .count('* as count');
        childrenCount = result[0] ? Number(result[0]['count']) : 0;
      }

      sendSuccess(res, {
        ...category,
        childrenCount,
        isDeleted: category.name.startsWith('[已删除]') || category.status === 0,
      }, 'Category details retrieved successfully');
    }
  );

  /**
   * Restore soft deleted category (恢复假删除)
   * PUT /api/v1/categories/:table/:id/restore
   */
  static restoreCategory = asyncHandler(
    async (req: Request, res: Response) => {
      const table = req.params.table as string;
      const id = parseInt(req.params.id as string);

      // Map old table names to levels for backward compatibility
      const tableToLevel: { [key: string]: number } = {
        'phone_brands': 1,
        'phone_models': 2,
        'phone_sub_models': 3
      };

      if (!tableToLevel[table]) {
        sendError(res, 'Invalid table name', 400);
        return;
      }

      const level = tableToLevel[table];

      // Check if category exists
      const existing = await db('phone_categories')
        .where('id', id)
        .where('level', level)
        .first();
      if (!existing) {
        sendError(res, 'Category not found', 404);
        return;
      }

      // Check if it's actually deleted
      if (!existing.name.startsWith('[已删除]') && existing.status !== 0) {
        sendError(res, 'Category is not deleted', 400);
        return;
      }

      // Restore the category by removing the [已删除] prefix and enabling status
      const restoredName = existing.name.replace(/^\[已删除\]/, '');

      await db('phone_categories')
        .where('id', id)
        .update({
          name: restoredName,
          status: 1, // 恢复为启用状态
          updated_at: db.fn.now()
        });

      sendSuccess(res, null, 'Category restored successfully');
    }
  );

  /**
   * Get price information for a category
   * GET /api/v1/categories/:table/:id/price
   */
  static getCategoryPrice = asyncHandler(
    async (req: Request, res: Response) => {
      const table = req.params.table as string;
      const id = parseInt(req.params.id as string);

      // Map old table names to levels for backward compatibility
      const tableToLevel: { [key: string]: number } = {
        'phone_brands': 1,
        'phone_models': 2,
        'phone_sub_models': 3
      };

      if (!tableToLevel[table]) {
        sendError(res, 'Invalid table name', 400);
        return;
      }

      const level = tableToLevel[table];

      // Get category details from phone_categories table
      const category = await db('phone_categories')
        .where('id', id)
        .where('level', level)
        .first();
      if (!category) {
        sendError(res, 'Category not found', 404);
        return;
      }

      // Get price tags for this category based on new structure
      let priceQuery;
      if (level === 3) {
        // For sub-models (level 3), get prices through memory_specs
        priceQuery = db('price_tags as pt')
          .join('memory_specs as ms', 'pt.memory_spec_id', 'ms.id')
          .where('ms.category_id', id)
          .select('pt.*', 'ms.memory_size')
          .orderBy('pt.updated_at', 'desc');
      } else {
        // For brands (level 1) and models (level 2), get prices through hierarchy
        let categoryIds = [id];

        if (level === 1) {
          // For brands, get all models under this brand
          const models = await db('phone_categories')
            .where('level', 2)
            .where('parent_id', id)
            .where('status', 1)
            .select('id');
          const modelIds = models.map(m => m.id);

          // Get all sub-models under these models
          if (modelIds.length > 0) {
            const subModels = await db('phone_categories')
              .where('level', 3)
              .whereIn('parent_id', modelIds)
              .where('status', 1)
              .select('id');
            categoryIds = subModels.map(sm => sm.id);
          }
        } else if (level === 2) {
          // For models, get all sub-models under this model
          const subModels = await db('phone_categories')
            .where('level', 3)
            .where('parent_id', id)
            .where('status', 1)
            .select('id');
          categoryIds = subModels.map(sm => sm.id);
        }

        if (categoryIds.length > 0) {
          priceQuery = db('price_tags as pt')
            .join('memory_specs as ms', 'pt.memory_spec_id', 'ms.id')
            .whereIn('ms.category_id', categoryIds)
            .select('pt.*', 'ms.memory_size', 'ms.category_id')
            .orderBy('pt.updated_at', 'desc');
        } else {
          priceQuery = db('price_tags').where('id', -1); // Return empty result
        }
      }

      const prices = await priceQuery;

      // Log the price view action to operation_logs table
      await db('operation_logs').insert({
        operation_type: 'PRICE_VIEW',
        table_name: 'phone_categories',
        record_id: id.toString(),
        operation_data: JSON.stringify({
          category_level: level,
          category_name: category.name,
          price_count: prices.length
        }),
        ip_address: req.ip,
        user_agent: req.get('User-Agent'),
        created_at: db.fn.now()
      }).catch(() => {
        console.log('Failed to log price view action');
      });

      sendSuccess(res, {
        category,
        prices,
        totalPrices: prices.length,
      }, 'Category price information retrieved successfully');
    }
  );

  /**
   * Get price trend analysis (涨跌结果)
   * GET /api/v1/categories/:table/:id/price-trend
   */
  static getPriceTrend = asyncHandler(
    async (req: Request, res: Response) => {
      const table = req.params.table as string;
      const id = parseInt(req.params.id as string);
      const days = parseInt(req.query.days as string) || 30; // Default to 30 days

      // Map old table names to levels for backward compatibility
      const tableToLevel: { [key: string]: number } = {
        'phone_brands': 1,
        'phone_models': 2,
        'phone_sub_models': 3
      };

      if (!tableToLevel[table]) {
        sendError(res, 'Invalid table name', 400);
        return;
      }

      const level = tableToLevel[table];

      // Get category details from phone_categories table
      const category = await db('phone_categories')
        .where('id', id)
        .where('level', level)
        .first();
      if (!category) {
        sendError(res, 'Category not found', 404);
        return;
      }

      // Get price history for trend analysis based on new structure
      let historyQuery;
      const dateFrom = new Date();
      dateFrom.setDate(dateFrom.getDate() - days);

      if (level === 3) {
        // For sub-models (level 3), get price history through memory_specs
        historyQuery = db('price_history as ph')
          .join('price_tags as pt', 'ph.tag_id', 'pt.id')
          .join('memory_specs as ms', 'pt.memory_spec_id', 'ms.id')
          .where('ms.category_id', id)
          .where('ph.created_at', '>=', dateFrom)
          .select('ph.*', 'pt.tag_name', 'pt.group_name', 'ms.memory_size')
          .orderBy('ph.created_at', 'asc');
      } else {
        // For brands (level 1) and models (level 2), get price history through hierarchy
        let categoryIds = [id];

        if (level === 1) {
          // For brands, get all sub-models under this brand
          const models = await db('phone_categories')
            .where('level', 2)
            .where('parent_id', id)
            .where('status', 1)
            .select('id');
          const modelIds = models.map(m => m.id);

          if (modelIds.length > 0) {
            const subModels = await db('phone_categories')
              .where('level', 3)
              .whereIn('parent_id', modelIds)
              .where('status', 1)
              .select('id');
            categoryIds = subModels.map(sm => sm.id);
          }
        } else if (level === 2) {
          // For models, get all sub-models under this model
          const subModels = await db('phone_categories')
            .where('level', 3)
            .where('parent_id', id)
            .where('status', 1)
            .select('id');
          categoryIds = subModels.map(sm => sm.id);
        }

        if (categoryIds.length > 0) {
          historyQuery = db('price_history as ph')
            .join('price_tags as pt', 'ph.tag_id', 'pt.id')
            .join('memory_specs as ms', 'pt.memory_spec_id', 'ms.id')
            .whereIn('ms.category_id', categoryIds)
            .where('ph.created_at', '>=', dateFrom)
            .select('ph.*', 'pt.tag_name', 'pt.group_name', 'ms.memory_size')
            .orderBy('ph.created_at', 'asc');
        } else {
          historyQuery = db('price_history').where('id', -1); // Return empty result
        }
      }

      const priceHistory = await historyQuery;

      // Analyze price trends
      const trendAnalysis = this.analyzePriceTrend(priceHistory);

      // Log the trend view action to operation_logs table
      await db('operation_logs').insert({
        operation_type: 'PRICE_TREND_VIEW',
        table_name: 'phone_categories',
        record_id: id.toString(),
        operation_data: JSON.stringify({
          category_level: level,
          category_name: category.name,
          analysis_days: days,
          history_count: priceHistory.length
        }),
        ip_address: req.ip,
        user_agent: req.get('User-Agent'),
        created_at: db.fn.now()
      }).catch(() => {
        console.log('Failed to log price trend view action');
      });

      sendSuccess(res, {
        category,
        days,
        priceHistory,
        trendAnalysis,
      }, 'Price trend analysis completed successfully');
    }
  );

  /**
   * Analyze price trend data
   */
  private static analyzePriceTrend(priceHistory: any[]) {
    if (priceHistory.length === 0) {
      return {
        trend: 'no_data',
        message: '暂无价格数据',
        changeCount: 0,
        avgChange: 0,
        maxPrice: 0,
        minPrice: 0,
      };
    }

    const changes = priceHistory.map(record => {
      const oldPrice = parseFloat(record.old_price) || 0;
      const newPrice = parseFloat(record.new_price) || 0;
      return newPrice - oldPrice;
    });

    const totalChange = changes.reduce((sum, change) => sum + change, 0);
    const avgChange = totalChange / changes.length;
    const prices = priceHistory.map(record => parseFloat(record.new_price));
    const maxPrice = Math.max(...prices);
    const minPrice = Math.min(...prices);

    let trend = 'stable';
    let message = '价格相对稳定';

    if (avgChange > 0) {
      trend = 'rising';
      message = `价格呈上涨趋势，平均涨幅 ¥${avgChange.toFixed(2)}`;
    } else if (avgChange < 0) {
      trend = 'falling';
      message = `价格呈下跌趋势，平均跌幅 ¥${Math.abs(avgChange).toFixed(2)}`;
    }

    return {
      trend,
      message,
      changeCount: changes.length,
      avgChange: parseFloat(avgChange.toFixed(2)),
      maxPrice: parseFloat(maxPrice.toFixed(2)),
      minPrice: parseFloat(minPrice.toFixed(2)),
      totalChange: parseFloat(totalChange.toFixed(2)),
      priceRange: parseFloat((maxPrice - minPrice).toFixed(2)),
    };
  }

  /**
   * Get complete model details with pricing information by sub-model ID
   * GET /api/v1/categories/sub-models/:subModelId/complete
   */
  static getCompleteModelDetails = asyncHandler(
    async (req: Request, res: Response) => {
      const subModelId = parseInt(req.params.subModelId as string);

      // Get sub-model details first (level = 3)
      const subModel = await db('phone_categories')
        .where('id', subModelId)
        .where('level', 3)
        .first();
      if (!subModel) {
        sendError(res, 'Sub-model not found', 404);
        return;
      }

      // Get model details (level = 2)
      const model = await db('phone_categories')
        .where('id', subModel.parent_id)
        .where('level', 2)
        .first();
      if (!model) {
        sendError(res, 'Model not found', 404);
        return;
      }

      // Get brand information (level = 1)
      const brand = await db('phone_categories')
        .where('id', model.parent_id)
        .where('level', 1)
        .first();
      if (!brand) {
        sendError(res, 'Brand not found', 404);
        return;
      }

      // Build sub_model array with pricing info using new structure
      const subModelArray = [];
      const allTags = [];

      try {
        // Get memory specs for this sub-model
        const memorySpecs = await db('memory_specs')
          .where('category_id', subModelId)
          .where('status', 1)
          .orderBy('id')
          .catch(() => []);

        if (memorySpecs.length > 0) {
          // Use memory_specs approach
          for (const memSpec of memorySpecs) {
            const highestPriceTag = await db('price_tags')
              .where('memory_spec_id', memSpec.id)
              .where('status', 1)
              .orderBy('price_rate', 'desc')
              .first()
              .catch(() => null);

            subModelArray.push({
              id: subModelId,
              sub_model_id: memSpec.id,
              price: highestPriceTag ? highestPriceTag.price_rate : memSpec.base_price || 1,
              tag_name: memSpec.memory_size || '未知规格'
            });

            // Get tags for this memory spec
            const tags = await db('price_tags')
              .where('memory_spec_id', memSpec.id)
              .where('status', 1)
              .orderBy('group_name', 'asc')
              .orderBy('price_rate', 'desc')
              .catch(() => []);

            for (const tag of tags) {
              allTags.push({
                sub_model_id: memSpec.id,
                id: tag.id,
                price_rate: tag.price_rate,
                tag_name: tag.tag_name,
                group_name: tag.group_name
              });
            }
          }
        } else {
          // Fallback: create a default entry if no memory specs found
          subModelArray.push({
            id: subModelId,
            sub_model_id: subModelId,
            price: 1,
            tag_name: subModel.name
          });
        }
      } catch (error) {
        console.log('Error querying memory specs and price tags:', error);
        // Return default entry if there's an error
        subModelArray.push({
          id: subModelId,
          sub_model_id: subModelId,
          price: 1,
          tag_name: subModel.name
        });
      }

      // Parse wechat_tag if it exists
      let wechatTag = {};
      if (subModel.wechat_tag) {
        try {
          wechatTag = typeof subModel.wechat_tag === 'string'
            ? JSON.parse(subModel.wechat_tag)
            : subModel.wechat_tag;
        } catch (e) {
          console.log('Failed to parse wechat_tag:', e);
        }
      }

      // Build response structure - using the requested sub-model as the main model
      const response = {
        model: {
          id: subModelId, // Use sub-model ID as requested
          parent_id: model.id,
          level: 3,
          name: subModel.name, // Use sub-model name
          sort_index: subModel.sort_index,
          remake: subModel.remake || "",
          wechat_tag: wechatTag
        },
        brand: {
          id: brand.id,
          parent_id: brand.parent_id,
          level: 1,
          name: brand.name,
          sort_index: brand.sort_index,
          remake: brand.remake || ""
        },
        classify: {
          id: brand.id, // Use brand as classify
          parent_id: brand.parent_id,
          level: 1,
          name: brand.name,
          sort_index: brand.sort_index,
          remake: brand.remake || ""
        },
        sub_model: subModelArray,
        tags: allTags
      };

      // Add debug information
      const debugInfo = {
        subModelId,
        modelId: model.id,
        brandId: brand.id,
        subModelArrayLength: subModelArray.length,
        allTagsLength: allTags.length
      };

      sendSuccess(res, {
        ...response,
        debug: debugInfo
      }, 'Complete model details retrieved successfully');
    }
  );

  /**
   * Debug endpoint to check database structure
   * GET /api/v1/categories/debug/:subModelId
   */
  static debugSubModelData = asyncHandler(
    async (req: Request, res: Response) => {
      const subModelId = parseInt(req.params.subModelId as string);

      try {
        // Get sub-model details from phone_categories (level = 3)
        const subModel = await db('phone_categories')
          .where('id', subModelId)
          .where('level', 3)
          .first();

        // Get model details (level = 2)
        const model = subModel ? await db('phone_categories')
          .where('id', subModel.parent_id)
          .where('level', 2)
          .first() : null;

        // Get brand details (level = 1)
        const brand = model ? await db('phone_categories')
          .where('id', model.parent_id)
          .where('level', 1)
          .first() : null;

        // Check memory specs for this sub-model
        const memorySpecs = await db('memory_specs')
          .where('category_id', subModelId)
          .catch(e => ({ error: e.message }));

        // Check price tags through memory specs
        let priceTags: any = [];
        if (Array.isArray(memorySpecs) && memorySpecs.length > 0) {
          const memSpecIds = memorySpecs.map(ms => ms.id);
          priceTags = await db('price_tags')
            .whereIn('memory_spec_id', memSpecIds)
            .limit(10)
            .catch(e => ({ error: e.message }));
        }

        // Get table structures
        const tableStructures = {
          phone_categories_columns: await db('phone_categories').columnInfo().catch(e => e.message),
          memory_specs_columns: await db('memory_specs').columnInfo().catch(e => e.message),
          price_tags_columns: await db('price_tags').columnInfo().catch(e => e.message)
        };

        sendSuccess(res, {
          subModel,
          model,
          brand,
          memorySpecs,
          priceTags,
          tableStructures,
          newDatabaseStructure: true
        }, 'Debug information retrieved (new database structure)');

      } catch (error: any) {
        sendError(res, `Debug error: ${error.message}`, 500);
      }
    }
  );

  /**
   * Get memory specs for a sub-model (category level 3)
   * GET /api/v1/categories/sub-models/:subModelId/memory-specs
   */
  static getMemorySpecs = asyncHandler(
    async (req: Request, res: Response) => {
      const subModelId = parseInt(req.params.subModelId as string);

      // Check if sub-model exists
      const subModel = await db('phone_categories')
        .where('id', subModelId)
        .where('level', 3)
        .first();
      if (!subModel) {
        sendError(res, 'Sub-model not found', 404);
        return;
      }

      // Get memory specs for this sub-model
      const memorySpecs = await db('memory_specs')
        .where('category_id', subModelId)
        .where('status', 1)
        .orderBy('id');

      sendSuccess(res, {
        subModel,
        memorySpecs,
        total: memorySpecs.length
      }, 'Memory specs retrieved successfully');
    }
  );

  /**
   * Get price tags for a memory spec
   * GET /api/v1/categories/memory-specs/:memorySpecId/price-tags
   */
  static getPriceTagsByMemorySpec = asyncHandler(
    async (req: Request, res: Response) => {
      const memorySpecId = parseInt(req.params.memorySpecId as string);

      // Check if memory spec exists
      const memorySpec = await db('memory_specs')
        .where('id', memorySpecId)
        .first();
      if (!memorySpec) {
        sendError(res, 'Memory spec not found', 404);
        return;
      }

      // Get price tags for this memory spec
      const priceTags = await db('price_tags')
        .where('memory_spec_id', memorySpecId)
        .where('status', 1)
        .orderBy('group_name')
        .orderBy('price_rate', 'desc');

      sendSuccess(res, {
        memorySpec,
        priceTags,
        total: priceTags.length
      }, 'Price tags retrieved successfully');
    }
  );

  /**
   * Sync categories from external API (placeholder for future implementation)
   * POST /api/v1/categories/sync
   */
  static syncCategories = asyncHandler(
    async (req: Request, res: Response) => {
      // Log the sync operation
      await db('operation_logs').insert({
        operation_type: 'SYNC_CATEGORY',
        table_name: 'phone_categories',
        operation_data: JSON.stringify({
          action: 'manual_sync_request',
          timestamp: new Date().toISOString()
        }),
        ip_address: req.ip,
        user_agent: req.get('User-Agent'),
        created_at: db.fn.now()
      });

      sendSuccess(res, {
        message: 'Category sync functionality is ready for implementation',
        status: 'placeholder'
      }, 'Sync endpoint ready');
    }
  );

  /**
   * Get price rankings (涨跌榜)
   * GET /api/v1/categories/price-rankings
   */
  static getPriceRankings = asyncHandler(
    async (req: Request, res: Response) => {
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const rankingType = req.query.type as string || 'RISE_RANKING'; // RISE_RANKING | FALL_RANKING
      const date = req.query.date as string; // YYYY-MM-DD format
      const categoryLevel = req.query.category_level as string; // 1, 2, 3
      const offset = (page - 1) * limit;

      // Validate ranking type
      if (!['RISE_RANKING', 'FALL_RANKING'].includes(rankingType)) {
        sendError(res, 'Invalid ranking type. Must be RISE_RANKING or FALL_RANKING', 400);
        return;
      }

      // Use today's date if not specified
      const targetDate = date || new Date().toISOString().split('T')[0];

      // Build query
      let query = db('price_trend_rankings')
        .where('ranking_date', targetDate)
        .where('ranking_type', rankingType)
        .where('status', 1)
        .orderBy('ranking_position', 'asc');

      // Filter by category level if specified
      if (categoryLevel) {
        const level = parseInt(categoryLevel);
        if ([1, 2, 3].includes(level)) {
          query = query.where('category_level', level);
        }
      }

      // Get paginated results
      const rankings = await query.limit(limit).offset(offset);

      // Get total count with same filters
      let countQuery = db('price_trend_rankings')
        .where('ranking_date', targetDate)
        .where('ranking_type', rankingType)
        .where('status', 1)
        .count('* as total');

      if (categoryLevel) {
        const level = parseInt(categoryLevel);
        if ([1, 2, 3].includes(level)) {
          countQuery = countQuery.where('category_level', level);
        }
      }

      const countResult = await countQuery;
      const total = countResult[0] ? Number(countResult[0]['total']) : 0;

      // Log the ranking view action
      await db('operation_logs').insert({
        operation_type: 'PRICE_RANKING_VIEW',
        table_name: 'price_trend_rankings',
        operation_data: JSON.stringify({
          ranking_type: rankingType,
          ranking_date: targetDate,
          category_level: categoryLevel,
          page,
          limit,
          total_results: total
        }),
        ip_address: req.ip,
        user_agent: req.get('User-Agent'),
        created_at: db.fn.now()
      }).catch(() => {
        console.log('Failed to log price ranking view action');
      });

      sendSuccess(res, {
        list: rankings,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
        meta: {
          ranking_type: rankingType,
          ranking_date: targetDate,
          category_level: categoryLevel || 'all'
        }
      }, 'Price rankings retrieved successfully');
    }
  );

  /**
   * Get price ranking statistics
   * GET /api/v1/categories/price-rankings/stats
   */
  static getPriceRankingStats = asyncHandler(
    async (req: Request, res: Response) => {
      const date = req.query.date as string; // YYYY-MM-DD format
      const targetDate = date || new Date().toISOString().split('T')[0];

      try {
        // Get basic statistics
        const statsQuery = `
          SELECT
            ranking_type,
            COUNT(*) as total_count,
            AVG(ABS(change_percentage)) as avg_change_percentage,
            MAX(ABS(change_percentage)) as max_change_percentage,
            MIN(ABS(change_percentage)) as min_change_percentage,
            SUM(CASE WHEN trend_type = 'RISE' THEN 1 ELSE 0 END) as rise_count,
            SUM(CASE WHEN trend_type = 'FALL' THEN 1 ELSE 0 END) as fall_count,
            SUM(CASE WHEN trend_type = 'STABLE' THEN 1 ELSE 0 END) as stable_count
          FROM price_trend_rankings
          WHERE ranking_date = ? AND status = 1
          GROUP BY ranking_type
        `;

        const statsResult = await db.raw(statsQuery, [targetDate]);
        const stats = statsResult[0] || [];

        // Get category level distribution
        const levelDistQuery = `
          SELECT
            category_level,
            ranking_type,
            COUNT(*) as count
          FROM price_trend_rankings
          WHERE ranking_date = ? AND status = 1
          GROUP BY category_level, ranking_type
          ORDER BY category_level, ranking_type
        `;

        const levelDistResult = await db.raw(levelDistQuery, [targetDate]);
        const levelDistribution = levelDistResult[0] || [];

        // Get top movers
        const topRiseQuery = `
          SELECT category_name, brand_name, model_name, sub_model_name,
                 change_percentage, current_price, previous_price
          FROM price_trend_rankings
          WHERE ranking_date = ? AND ranking_type = 'RISE_RANKING' AND status = 1
          ORDER BY ranking_position ASC
          LIMIT 5
        `;

        const topFallQuery = `
          SELECT category_name, brand_name, model_name, sub_model_name,
                 change_percentage, current_price, previous_price
          FROM price_trend_rankings
          WHERE ranking_date = ? AND ranking_type = 'FALL_RANKING' AND status = 1
          ORDER BY ranking_position ASC
          LIMIT 5
        `;

        const topRiseResult = await db.raw(topRiseQuery, [targetDate]);
        const topFallResult = await db.raw(topFallQuery, [targetDate]);

        const topRise = topRiseResult[0] || [];
        const topFall = topFallResult[0] || [];

        // Log the stats view action
        await db('operation_logs').insert({
          operation_type: 'PRICE_RANKING_STATS_VIEW',
          table_name: 'price_trend_rankings',
          operation_data: JSON.stringify({
            ranking_date: targetDate,
            stats_count: stats.length,
            timestamp: new Date().toISOString()
          }),
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          created_at: db.fn.now()
        }).catch(() => {
          console.log('Failed to log price ranking stats view action');
        });

        sendSuccess(res, {
          date: targetDate,
          statistics: stats,
          levelDistribution,
          topMovers: {
            topRise,
            topFall
          }
        }, 'Price ranking statistics retrieved successfully');

      } catch (error) {
        logger.error('Error getting price ranking statistics:', error);
        sendError(res, 'Failed to retrieve price ranking statistics', 500);
      }
    }
  );

  /**
   * Manually trigger price ranking calculation
   * POST /api/v1/categories/price-rankings/calculate
   */
  static calculatePriceRankings = asyncHandler(
    async (req: Request, res: Response) => {
      try {
        // Import the calculator dynamically to avoid circular dependencies
        const PriceRankingCalculator = (await import('../scripts/calculatePriceRankings')).default;
        const calculator = new PriceRankingCalculator();

        // Execute the calculation
        await calculator.execute();

        // Log the manual calculation action
        await db('operation_logs').insert({
          operation_type: 'MANUAL_CALCULATE_PRICE_RANKINGS',
          table_name: 'price_trend_rankings',
          operation_data: JSON.stringify({
            action: 'manual_trigger',
            timestamp: new Date().toISOString()
          }),
          ip_address: req.ip,
          user_agent: req.get('User-Agent'),
          created_at: db.fn.now()
        });

        sendSuccess(res, {
          message: 'Price rankings calculation completed successfully',
          timestamp: new Date().toISOString()
        }, 'Price rankings calculated successfully');

      } catch (error) {
        logger.error('Error calculating price rankings:', error);
        sendError(res, 'Failed to calculate price rankings', 500);
      }
    }
  );

  /**
   * Search categories by name across all levels
   * GET /api/v1/categories/search
   */
  static searchCategories = asyncHandler(
    async (req: Request, res: Response) => {
      const search = req.query.search as string;
      const level = req.query.level as string;
      const page = parseInt(req.query.page as string) || 1;
      const limit = parseInt(req.query.limit as string) || 20;
      const offset = (page - 1) * limit;

      if (!search || search.trim().length === 0) {
        sendError(res, 'Search parameter is required', 400);
        return;
      }

      let query = db('phone_categories')
        .where('status', 1)
        .where('level', 3)
        .where('name', 'like', `%${search.trim()}%`)
        .orderBy(['level', 'sort_index', 'id']);

      // Filter by level if specified
      if (level && ['1', '2', '3'].includes(level)) {
        query = query.where('level', parseInt(level));
      }

      // Get paginated results
      const categories = await query.limit(limit).offset(offset);

      // Get total count with same filters
      let countQuery = db('phone_categories')
        .where('status', 1)
        .where('name', 'like', `%${search.trim()}%`)
        .count('* as total');

      if (level && ['1', '2', '3'].includes(level)) {
        countQuery = countQuery.where('level', parseInt(level));
      }

      const countResult = await countQuery;
      const total = countResult[0] ? Number(countResult[0]['total']) : 0;

      // Enhance results with parent information
      const enhancedCategories = [];
      for (const category of categories) {
        let parentInfo = null;
        let brandInfo = null;

        if (category.parent_id) {
          parentInfo = await db('phone_categories')
            .where('id', category.parent_id)
            .first();

          // If this is a sub-model (level 3), also get brand info
          if (category.level === 3 && parentInfo?.parent_id) {
            brandInfo = await db('phone_categories')
              .where('id', parentInfo.parent_id)
              .first();
          }
        }

        enhancedCategories.push({
          ...category,
          parent: parentInfo,
          brand: brandInfo,
          level_name: category.level === 1 ? '品牌' : category.level === 2 ? '型号' : '子型号'
        });
      }

      // Log the search action
      await db('operation_logs').insert({
        operation_type: 'CATEGORY_SEARCH',
        table_name: 'phone_categories',
        operation_data: JSON.stringify({
          search_term: search,
          level: level || 'all',
          results_count: total
        }),
        ip_address: req.ip,
        user_agent: req.get('User-Agent'),
        created_at: db.fn.now()
      }).catch(() => {
        console.log('Failed to log search action');
      });

      sendSuccess(res, {
        list: enhancedCategories,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
        meta: {
          search_term: search,
          level: level || 'all'
        }
      }, 'Categories search completed successfully');
    }
  );

  /**
   * Get complete category details by ID (similar to yizhanhuishou.com API structure)
   * GET /api/v1/categories/model/:id
   */
  static getModelComplete = asyncHandler(
    async (req: Request, res: Response) => {
      const id = parseInt(req.params.id as string);

      if (!id || isNaN(id)) {
        sendError(res, 'Invalid category ID', 400);
        return;
      }

      // Get the main category (model/sub-model)
      const model = await db('phone_categories')
        .where('id', id)
        .where('status', 1)
        .first();

      if (!model) {
        sendError(res, 'Category not found', 404);
        return;
      }

      // Get brand information (level 1)
      let brand = null;
      let classify = null;

      if (model.level === 3) {
        // For sub-model, get parent model first, then brand
        const parentModel = await db('phone_categories')
          .where('id', model.parent_id)
          .first();

        if (parentModel) {
          classify = parentModel;
          brand = await db('phone_categories')
            .where('id', parentModel.parent_id)
            .first();
        }
      } else if (model.level === 2) {
        // For model, get brand directly
        classify = model;
        brand = await db('phone_categories')
          .where('id', model.parent_id)
          .first();
      } else if (model.level === 1) {
        // For brand, it is the brand itself
        brand = model;
        classify = model;
      }

      // Get sub-models (memory specifications)
      const subModels = await db('memory_specs')
        .where('category_id', id)
        .where('status', 1)
        .select('id as sub_model_id', 'memory_size as tag_name', 'base_price as price')
        .orderBy('id');

      // Get price tags for all memory specs
      const memorySpecIds = subModels.map(sm => sm.sub_model_id);
      let tags: any[] = [];

      if (memorySpecIds.length > 0) {
        tags = await db('price_tags')
          .whereIn('memory_spec_id', memorySpecIds)
          .where('status', 1)
          .select(
            'memory_spec_id as sub_model_id',
            'id',
            'price_rate',
            'tag_name',
            'group_name'
          )
          .orderBy(['group_name', 'tag_name']);
      }

      // Format sub_model data to include category id
      const formattedSubModels = subModels.map(sm => ({
        id: id, // Category ID
        sub_model_id: sm.sub_model_id,
        price: sm.price || 1,
        tag_name: sm.tag_name
      }));

      // Log the view action
      await db('operation_logs').insert({
        operation_type: 'MODEL_DETAIL_VIEW',
        table_name: 'phone_categories',
        operation_data: JSON.stringify({
          category_id: id,
          category_name: model.name,
          level: model.level,
          timestamp: new Date().toISOString()
        }),
        ip_address: req.ip,
        user_agent: req.get('User-Agent'),
        created_at: db.fn.now()
      }).catch(() => {
        console.log('Failed to log model detail view action');
      });

      // Return data in the same structure as yizhanhuishou.com
      sendSuccess(res, {
        model: {
          id: model.id,
          parent_id: model.parent_id,
          level: model.level,
          name: model.name,
          sort_index: model.sort_index,
          remake: model.remake || "",
          wechat_tag: {
            dianliang: "注意：电池电量≥75%",
            gaobaotianshu: "高保充新价格请来电咨询、全套包装带原装配件加30-300元",
            aisishalouquanlv: true,
            wuliangdianliangban: true,
            wulaohuatoutu: true
          }
        },
        brand: brand ? {
          id: brand.id,
          parent_id: brand.parent_id,
          level: brand.level,
          name: brand.name,
          sort_index: brand.sort_index,
          remake: brand.remake || ""
        } : null,
        classify: classify ? {
          id: classify.id,
          parent_id: classify.parent_id,
          level: classify.level,
          name: classify.name,
          sort_index: classify.sort_index,
          remake: classify.remake || ""
        } : null,
        sub_model: formattedSubModels,
        tags: tags
      }, 'Model details retrieved successfully');
    }
  );
}
