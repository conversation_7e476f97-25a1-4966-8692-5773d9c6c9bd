import { Request, Response, NextFunction } from 'express';
import { z } from 'zod';
import { asyncHandler } from './errorHandler';

/**
 * Validation middleware factory
 */
export const validate = (schema: {
  body?: z.ZodSchema;
  query?: z.ZodSchema;
  params?: z.ZodSchema;
}) => {
  return asyncHandler(
    async (req: Request, res: Response, next: NextFunction) => {
      // Validate request body
      if (schema.body) {
        req.body = schema.body.parse(req.body);
      }

      // Validate query parameters
      if (schema.query) {
        req.query = schema.query.parse(req.query);
      }

      // Validate route parameters
      if (schema.params) {
        req.params = schema.params.parse(req.params);
      }

      next();
    }
  );
};
