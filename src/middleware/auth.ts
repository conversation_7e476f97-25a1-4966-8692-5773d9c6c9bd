import { Response, NextFunction } from 'express';
import { AuthenticatedRequest, AppError, UserRole } from '../types';
import { verifyToken, extractTokenFromHeader } from '../utils/jwt';
import { asyncHandler } from './errorHandler';

/**
 * Authentication middleware
 */
export const authenticate = asyncHandler(
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    const authHeader = req.headers.authorization;
    const token = extractTokenFromHeader(authHeader);

    if (!token) {
      throw new AppError('Access token is required', 401);
    }

    try {
      const payload = verifyToken(token);
      req.user = payload;
      next();
    } catch (error) {
      throw new AppError('Invalid or expired token', 401);
    }
  }
);

/**
 * Authorization middleware factory
 */
export const authorize = (...roles: UserRole[]) => {
  return asyncHandler(
    async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
      if (!req.user) {
        throw new AppError('Authentication required', 401);
      }

      if (roles.length > 0 && !roles.includes(req.user.role)) {
        throw new AppError('Insufficient permissions', 403);
      }

      next();
    }
  );
};

/**
 * Optional authentication middleware (doesn't throw error if no token)
 */
export const optionalAuth = asyncHandler(
  async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    const authHeader = req.headers.authorization;
    const token = extractTokenFromHeader(authHeader);

    if (token) {
      try {
        const payload = verifyToken(token);
        req.user = payload;
      } catch (error) {
        // Ignore token errors for optional auth
      }
    }

    next();
  }
);
